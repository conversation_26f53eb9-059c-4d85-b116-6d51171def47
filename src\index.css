  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


  @tailwind base;
  @tailwind components;
  @tailwind utilities;

  @layer base {
    html { -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }
    body { font-family: Inter, system-ui, -apple-system, "Segoe UI", Roboto, Arial, sans-serif; }
    ::selection { background: #2563eb; color: white; }
  }

  @layer components {
    .btn-primary { background: #2563eb; color: white; padding: .625rem 1.5rem; border-radius: .5rem; }
    .card-base { background: white; border-radius: .75rem; box-shadow: 0 1px 3px rgba(0,0,0,0.08); }
    .flight-card { border: 1px solid #f3f4f6; }
    .bg-flight-primary { background-color: #0066ff; }
    .bg-flight-bg { background-color: #87CEEB; }
    .bg-admin-primary { background-color: #6366f1; }
    .bg-admin-sidebar { background-color: #4c1d95; }
  }

  @layer utilities {
    .text-flight-primary { color: #0066ff; }
    .text-admin-primary { color: #6366f1; }
    .text-status-completed { color: #10b981; }
    .bg-neutral-50 { background-color: #fafafa; }
  }

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

/* keep default button styles minimal; demo.css defines .btn-primary */
button {
  font-family: inherit;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
