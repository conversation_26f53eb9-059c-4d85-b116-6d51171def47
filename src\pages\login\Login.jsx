import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import authService from '../../api/auth'
import Card, { CardBody } from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import Input from '../../components/ui/Input'

function parseJwt(token) {
  try {
    const parts = token.split('.')
    if (parts.length < 2) return null
    const payload = parts[1]
    // add padding if necessary
    const padded = payload.padEnd(payload.length + (4 - (payload.length % 4)) % 4, '=')
    const decoded = atob(padded.replace(/-/g, '+').replace(/_/g, '/'))
    return JSON.parse(decoded)
  } catch (e) {
    return null
  }
}

export default function Login() {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const navigate = useNavigate()

  async function handleSubmit(e) {
    e.preventDefault()
    setError('')
    setLoading(true)

    try {
      const raw = await authService.login(username, password)
      const token = raw?.token || raw?.accessToken || raw?.data?.token
      if (!token) throw new Error('No token returned')

      // store token
      authService.saveToken(token)

      // derive user info
      const backendUser = raw?.user || raw?.data || raw || {}
      const name = backendUser.name || backendUser.username || username
      const roleField = backendUser.role || backendUser.roles || backendUser.authorities

      let chosenRole = null
      if (Array.isArray(roleField)) chosenRole = roleField[0]
      else if (typeof roleField === 'string') chosenRole = roleField

      if (!chosenRole) {
        const payload = parseJwt(token) || {}
        const roleFromToken = payload.role || payload.roles || payload.authorities
        if (Array.isArray(roleFromToken)) chosenRole = roleFromToken[0]
        else if (typeof roleFromToken === 'string') chosenRole = roleFromToken
      }

      // normalize and navigate by role
      const norm = (r) => (r ? String(r).toLowerCase() : '')
      const nr = norm(chosenRole)
      if (nr.includes('admin')) {
        navigate('/admin', { state: { name, role: chosenRole } })
      } else if (nr.includes('passenger')) {
        navigate('/passenger', { state: { name, role: chosenRole } })
      } else {
        navigate('/staff', { state: { name, role: chosenRole || 'staff' } })
      }
    } catch (err) {
      // authService.postJson throws {status, body}
      const msg = err?.body?.message || err?.message || 'Login failed'
      setError(msg)
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-flight-bg/20 via-white to-flight-bg/10">
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-flight-primary rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
              <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-neutral-900 mb-2">Welcome Back</h1>
            <p className="text-neutral-600">Sign in to your airline management account</p>
          </div>

          <Card className="shadow-hover border-0 bg-white/80 backdrop-blur-sm">
            <CardBody className="p-8">
              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex">
                      <div className="ml-3">
                        <p className="text-sm text-red-800">{error}</p>
                      </div>
                    </div>
                  </div>
                )}

                <Input
                  label="Username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="Enter your username"
                  required
                  disabled={loading}
                  variant="flight"
                />

                <Input
                  label="Password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  required
                  disabled={loading}
                  variant="flight"
                />

                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  fullWidth
                  loading={loading}
                  disabled={loading}
                >
                  {loading ? 'Signing in...' : 'Sign in'}
                </Button>
              </form>

              <div className="mt-8">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-neutral-200" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-3 bg-white text-neutral-500">Quick access for testing</span>
                  </div>
                </div>

                <div className="mt-6 grid grid-cols-3 gap-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => { setUsername('admin1'); setPassword('adminpass'); }}
                    disabled={loading}
                  >
                    Admin
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => { setUsername('inflight1'); setPassword('inflightpass'); }}
                    disabled={loading}
                  >
                    Staff
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => { setUsername('passenger1'); setPassword('passpass'); }}
                    disabled={loading}
                  >
                    Passenger
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>

          <div className="text-center mt-8">
            <p className="text-sm text-neutral-600">
              Need help? Contact{' '}
              <a href="#" className="text-flight-primary hover:text-flight-dark font-medium">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
