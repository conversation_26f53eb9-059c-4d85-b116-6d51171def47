const BASE_URL = 'http://localhost:8090'

async function postJson(path, body, token) {
  const res = await fetch(`${BASE_URL}${path}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    },
    body: JSON.stringify(body),
  })
  const json = await res.json()
  if (!res.ok) throw { status: res.status, body: json }
  return json
}

export async function login(username, password) {
  return postJson('/api/auth/login', { username, password })
}

export async function validateToken(token) {
  return postJson('/api/auth/validate-token', {}, token)
}

export async function getRole(token) {
  const res = await fetch(`${BASE_URL}/api/auth/roles`, {
    headers: { Authorization: `Bearer ${token}` },
  })
  if (!res.ok) throw { status: res.status }
  return res.json()
}

export function saveToken(token) {
  localStorage.setItem('auth_token', token)
}

export function loadToken() {
  return localStorage.getItem('auth_token')
}

export function clearToken() {
  localStorage.removeItem('auth_token')
}

export default {
  login,
  validateToken,
  getRole,
  saveToken,
  loadToken,
  clearToken,
}
